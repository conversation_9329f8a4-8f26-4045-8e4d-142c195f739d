# `CmWechatGroupQueryService.queryUserGroupList` 接口测试用例

## 1. 接口概述

- **接口名称:** `CmWechatGroupQueryService.queryUserGroupList`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.queryUserGroupList(QueryUserGroupRequest)`
- **功能描述:** 分页查询客户的群组信息，支持按一账通号、微信昵称和部门ID列表进行筛选。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **3个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户基本信息 | `external_user_id`, `hbone_no`, `nick_name` | 作为查询的起点，用于筛选符合条件的客户。 |
| `cm_wechat_group_user_new` | 记录客户与群组的关联 | `external_user_id`, `chat_id`, `user_chat_flag` | 关联客户和群组，并标识客户是否在群中。 |
| `cm_wechat_group` | 存储群组基本信息 | `chat_id` | 提供群组的详细信息。 |

**数据准备核心思路:**
创建一个有效的测试数据，需要确保 `cm_wechat_cust_info` 中有客户记录，该客户在 `cm_wechat_group_user_new` 中有关联的群组记录，并且这些群组在 `cm_wechat_group` 中是存在的。

## 3. 输入参数 (`QueryUserGroupRequest`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 否 | 客户的一账通号。 |
| `wechatNickName` | `String` | 否 | 客户的微信昵称（支持模糊查询）。 |
| `deptIdList` | `List<Integer>` | 是 | 部门ID列表，用于限定查询范围。 |
| `pageNo` | `Integer` | 否 | 页码，默认为 `1`。 |
| `pageSize` | `Integer` | 否 | 每页大小，默认为 `10`。 |

## 4. 输出结果 (`UserGroupVO`)

- **成功:** 返回 `Response.ok(Data)`，其中 `Data` (`UserGroupVO`) 包含客户群组信息的列表（`userGroupDetailVOList`）、总条数（`totalNum`）和总页数（`totalPage`）。
- **失败:** 返回带有错误码和错误信息的 `Response` 对象，例如当 `deptIdList` 为空时，返回 `PARAM_ERROR`。

## 5. 测试用例

### 5.1. 正常场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 按 `hbOneNo` 精确查询 | 使用一个存在的 `hbOneNo` 进行查询，验证是否能返回正确的客户群组信息。 | 1. 数据库中存在 `hbOneNo` 为 `HB_A` 的客户。<br>2. 该客户关联了至少一个群组。 | `hbOneNo`: `HB_A`<br>`deptIdList`: `[101]` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.totalNum` 大于 `0`。<br>3. `Response.data.userGroupDetailVOList` 中包含 `hbOneNo` 为 `HB_A` 的记录。 |
| **TC-N-002** | 按 `wechatNickName` 模糊查询 | 使用一个存在的微信昵称关键字进行查询，验证是否能返回所有匹配的客户。 | 1. 数据库中存在微信昵称包含 `NickName_B` 的客户。<br>2. 这些客户关联了至少一个群组。 | `wechatNickName`: `NickName_B`<br>`deptIdList`: `[102]` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.totalNum` 大于 `0`。<br>3. 返回的记录中，`wechatNickName` 都包含 `NickName_B`。 |
| **TC-N-003** | 分页查询 | 提供 `pageNo` 和 `pageSize`，验证分页逻辑是否正确。 | 数据库中存在超过 `5` 条属于 `deptIdList: [103]` 的客户记录。 | `deptIdList`: `[103]`<br>`pageNo`: `2`<br>`pageSize`: `5` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.userGroupDetailVOList` 的数量应为 `5`（或最后一页的余数）。<br>3. `Response.data.totalPage` 计算正确。 |
| **TC-N-004** | 客户同时在多个群 | 查询一个同时加入了多个群的客户，验证 `groupIdList` 是否包含所有群ID。 | 客户 `HB_C` 同时加入了 `Group_X` 和 `Group_Y`。 | `hbOneNo`: `HB_C`<br>`deptIdList`: `[101]` | | 1. `Response.code` 为 `0`。<br>2. 返回的客户记录中，`groupIdList` 包含 `Group_X` 和 `Group_Y`。 |
| **TC-N-005** | 客户有历史退群记录 | 查询一个既有当前所在群，也有历史退群记录的客户。 | 客户 `HB_D` 当前在 `Group_Z`，但曾退出过 `Group_W`。 | `hbOneNo`: `HB_D`<br>`deptIdList`: `[101]` | | 1. `Response.code` 为 `0`。<br>2. 返回的客户记录中，`groupIdList` 包含 `Group_Z`，`hisGroupIdList` 包含 `Group_W`。 |

### 5.2. 异常及边界场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `deptIdList` 为空 | 测试当必填参数 `deptIdList` 为 `null` 或空列表时，接口是否返回参数错误。 | 无 | `deptIdList`: `[]` 或 `null` | | 1. `Response.code` 为 `PARAM_ERROR` 的错误码。<br>2. `Response.message` 包含参数错误的提示。 |
| **TC-E-002** | 查询无结果 | 使用一个不存在的 `hbOneNo` 进行查询，预期返回空列表。 | 数据库中不存在 `hbOneNo` 为 `NON_EXISTENT` 的客户。 | `hbOneNo`: `NON_EXISTENT`<br>`deptIdList`: `[101]` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.totalNum` 为 `0`。<br>3. `Response.data.userGroupDetailVOList` 为空列表。 |
| **TC-E-003** | `pageNo` 或 `pageSize` 为 `null` | 测试当分页参数为 `null` 时，是否能使用默认值（`pageNo=1`, `pageSize=10`）执行。 | 数据库中存在超过 `10` 条相关记录。 | `deptIdList`: `[101]`<br>`pageNo`: `null`<br>`pageSize`: `null` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.userGroupDetailVOList` 的数量为 `10`。 |
