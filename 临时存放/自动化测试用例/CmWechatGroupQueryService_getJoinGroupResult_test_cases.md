# `CmWechatGroupQueryService.getJoinGroupResult` 接口测试用例

## 1. 接口概述

- **接口名称:** `CmWechatGroupQueryService.getJoinGroupResult`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getJoinGroupResult(String, String)`
- **功能描述:** 根据一账通号（hbOneNo）和群组ID（groupId），查询客户在该群的加入状态（未加入、已加入、已退群）。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **2个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户基本信息 | `hbone_no`, `external_user_id` | 用于通过 `hbone_no` 找到客户的 `external_user_id`。 |
| `cm_wechat_group_user_new` | 记录客户与群组的关联 | `external_user_id`, `chat_id`, `user_chat_flag` | 通过 `external_user_id` 和 `chat_id` 查询客户与特定群组的关系。 |

**数据准备核心思路:**
测试数据需覆盖客户存在与不存在、客户与群组关联关系存在与不存在、以及关联关系中 `user_chat_flag` 的不同状态（0:在群, 1:退群）。

## 3. 输入参数 (`String`, `String`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 客户的唯一一账通号。 |
| `groupId` | `String` | 是 | 要查询的群组ID。 |

## 4. 输出结果 (`JoinGroupResultVO`)

- **成功:** 返回 `Response.ok(Data)`，其中 `Data` (`JoinGroupResultVO`) 包含 `joinGroupStatus`（1-未入群, 2-已入群, 3-已退群）、`joinGroupTime` 和 `existGroupTime`。
- **失败:** 如果输入参数为空，返回 `PARAM_ERROR`。

## 5. 测试用例

### 5.1. 正常场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 客户已加入群聊 | 查询一个确定在目标群组中的客户。 | 1. 客户 `HB_JOINED` 存在。<br>2. `cm_wechat_group_user_new` 中存在 `HB_JOINED` 和 `GROUP_ACTIVE` 的关联记录，且 `user_chat_flag=0`。 | `hbOneNo`: `HB_JOINED`<br>`groupId`: `GROUP_ACTIVE` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.joinGroupStatus` 为 `2` (已入群)。<br>3. `Response.data.joinGroupTime` 不为空。 |
| **TC-N-002** | 客户已退出群聊 | 查询一个确定已从目标群组中退出的客户。 | 1. 客户 `HB_LEFT` 存在。<br>2. `cm_wechat_group_user_new` 中存在 `HB_LEFT` 和 `GROUP_INACTIVE` 的关联记录，且 `user_chat_flag=1`。 | `hbOneNo`: `HB_LEFT`<br>`groupId`: `GROUP_INACTIVE` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.joinGroupStatus` 为 `3` (已退群)。<br>3. `Response.data.joinGroupTime` 和 `existGroupTime` 可能不为空。 |
| **TC-N-003** | 客户未加入过群聊 | 查询一个确定从未加入过目标群组的客户。 | 1. 客户 `HB_NEVER_JOINED` 存在。<br>2. `cm_wechat_group_user_new` 中不存在 `HB_NEVER_JOINED` 和 `GROUP_ANY` 的关联记录。 | `hbOneNo`: `HB_NEVER_JOINED`<br>`groupId`: `GROUP_ANY` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.joinGroupStatus` 为 `1` (未入群)。 |

### 5.2. 异常及边界场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `hbOneNo` 为 `null` | 测试当 `hbOneNo` 为 `null` 时，接口返回参数错误。 | 无 | `hbOneNo`: `null`<br>`groupId`: `GROUP_ANY` | | 1. `Response.code` 为 `PARAM_ERROR` 的错误码。<br>2. `Response.message` 包含参数错误的提示。 |
| **TC-E-002** | `groupId` 为空字符串 | 测试当 `groupId` 为空字符串时，接口返回参数错误。 | 无 | `hbOneNo`: `HB_ANY`<br>`groupId`: `""` | | 1. `Response.code` 为 `PARAM_ERROR` 的错误码。<br>2. `Response.message` 包含参数错误的提示。 |
| **TC-E-003** | `hbOneNo` 不存在 | 使用一个不存在的 `hbOneNo` 进行查询，应返回未入群。 | 数据库中不存在 `hbOneNo` 为 `NON_EXISTENT` 的客户。 | `hbOneNo`: `NON_EXISTENT`<br>`groupId`: `GROUP_ANY` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.joinGroupStatus` 为 `1` (未入群)。 |
| **TC-E-004** | `groupId` 不存在 | 使用一个存在的 `hbOneNo` 和一个不存在的 `groupId` 进行查询，应返回未入群。 | 1. 客户 `HB_ANY` 存在。<br>2. 数据库中不存在 `groupId` 为 `GROUP_NON_EXISTENT` 的群。 | `hbOneNo`: `HB_ANY`<br>`groupId`: `GROUP_NON_EXISTENT` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.joinGroupStatus` 为 `1` (未入群)。 |

## 接口代码逻辑脑图
```plantuml
@startmindmap
* getJoinGroupResult
** 参数校验
*** 反例: hbOneNo 为 null 或空字符串
**** 返回 PARAM_ERROR (400)
*** 反例: groupId 为 null 或空字符串
**** 返回 PARAM_ERROR (400)
** 核心业务逻辑
*** 查询客户 externalUserId (by hbOneNo)
**** 反例: externalUserId 未找到 (客户不存在)
***** 返回 `joinGroupStatus` = 1 (未入群)
**** 正例: 找到 externalUserId
***** 查询群关系 (by chatId, externalUserId)
****** 反例: 未找到群关系记录
******* 返回 `joinGroupStatus` = 1 (未入群)
****** 正例: 找到群关系记录
******* 判断 `userchatflag`
******** 正例: `userchatflag` == 0
********* 返回 `joinGroupStatus` = 2 (已入群)
******** 反例: `userchatflag` != 0
********* 返回 `joinGroupStatus` = 3 (已退群)
** 返回结果
*** 总是返回 `Response.ok(JoinGroupResultVO)`
@endmindmap
```
