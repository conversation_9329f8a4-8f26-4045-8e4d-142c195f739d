# `CmWechatGroupQueryService.getGroupIdByHbOneNo` 接口测试用例

## 1. 接口概述

- **接口名称:** `CmWechatGroupQueryService.getGroupIdByHbOneNo`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getGroupIdByHbOneNo(String)`
- **功能描述:** 根据客户的一账通号（hbOneNo）查询其当前所有正常在群的群组ID列表及对应的入群时间。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **2个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户基本信息 | `hbone_no`, `external_user_id` | 用于通过 `hbone_no` 找到客户的 `external_user_id`。 |
| `cm_wechat_group_user_new` | 记录客户与群组的关联 | `external_user_id`, `chat_id`, `user_chat_flag` | 通过 `external_user_id` 筛选出客户状态为正常（`user_chat_flag=0`）的群组记录。 |

**数据准备核心思路:**
创建一个有效的测试数据，需要确保 `cm_wechat_cust_info` 中存在一个客户记录，该客户的 `external_user_id` 在 `cm_wechat_group_user_new` 中至少有一条 `user_chat_flag` 为 `0` 的记录。

## 3. 输入参数 (`String`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 客户的唯一一账通号。 |

## 4. 输出结果 (`GroupIdVO`)

- **成功:** 返回 `Response.ok(Data)`，其中 `Data` (`GroupIdVO`) 包含一个 `groupIdList` 列表。列表中的每个对象都包含 `groupId` 和 `joinGroupTime`。
- **失败:** 如果 `hbOneNo` 为空或无效，或者客户不存在，通常返回一个空的 `groupIdList`，而不是业务异常。

## 5. 测试用例

### 5.1. 正常场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 客户在一个群中 | 使用一个只加入了一个群的客户 `hbOneNo` 进行查询。 | 1. 客户 `HB_SINGLE_GROUP` 存在于 `cm_wechat_cust_info`。<br>2. 该客户在 `cm_wechat_group_user_new` 中只有一条 `user_chat_flag=0` 的记录，关联群 `GROUP_A`。 | `hbOneNo`: `HB_SINGLE_GROUP` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 包含 `1` 个元素。<br>3. 列表中的 `groupId` 为 `GROUP_A`。 |
| **TC-N-002** | 客户在多个群中 | 使用一个加入了多个群的客户 `hbOneNo` 进行查询。 | 1. 客户 `HB_MULTI_GROUP` 存在。<br>2. 该客户在 `cm_wechat_group_user_new` 中有多条 `user_chat_flag=0` 的记录，关联群 `GROUP_B` 和 `GROUP_C`。 | `hbOneNo`: `HB_MULTI_GROUP` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 包含 `2` 个元素。<br>3. 列表中的 `groupId` 分别为 `GROUP_B` 和 `GROUP_C`。 |
| **TC-N-003** | 客户有在群和已退群记录 | 查询一个既有正常在群记录，也有已退群记录的客户。 | 1. 客户 `HB_MIX_STATUS` 存在。<br>2. 该客户在群 `GROUP_D` (`user_chat_flag=0`)，但已从群 `GROUP_E` 退出 (`user_chat_flag=1`)。 | `hbOneNo`: `HB_MIX_STATUS` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 只包含 `1` 个元素。<br>3. 列表中的 `groupId` 为 `GROUP_D`。 |

### 5.2. 异常及边界场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `hbOneNo` 为 `null` | 测试当 `hbOneNo` 为 `null` 时，接口是否能正常处理并返回空列表。 | 无 | `hbOneNo`: `null` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-002** | `hbOneNo` 为空字符串 | 测试当 `hbOneNo` 为空字符串时，接口是否能正常处理并返回空列表。 | 无 | `hbOneNo`: `""` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-003** | `hbOneNo` 不存在 | 使用一个在 `cm_wechat_cust_info` 中不存在的 `hbOneNo` 进行查询。 | 数据库中不存在 `hbOneNo` 为 `NON_EXISTENT` 的客户。 | `hbOneNo`: `NON_EXISTENT` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-004** | 客户未加入任何群 | 查询一个存在但未加入任何群组的客户。 | 客户 `HB_NO_GROUP` 存在，但在 `cm_wechat_group_user_new` 中没有任何 `user_chat_flag=0` 的记录。 | `hbOneNo`: `HB_NO_GROUP` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-005** | 客户已退出所有群 | 查询一个存在但已从所有群组中退出的客户。 | 客户 `HB_ALL_LEFT` 存在，但其在 `cm_wechat_group_user_new` 中的所有记录 `user_chat_flag` 均为 `1`。 | `hbOneNo`: `HB_ALL_LEFT` | | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
