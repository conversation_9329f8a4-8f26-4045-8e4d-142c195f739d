# `WechatCustRelationController` 接口测试用例

## 1. 接口概述

- **接口类型:** `HTTP`
- **接口名称:** `WechatCustRelationController`
- **接口路径:** `com.howbuy.crm.wechat.service.controller.WechatCustRelationController.selectRelationListByVo(List<CustConsultRelationVO>)`
- **功能描述:** 批量查询客户hboneNo 和 投顾consCode 关系

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **1个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_consult_relation` | 存储企业微信客户和投顾的关联关系 | `hbone_no`, `cons_code`, `status` | `hbone_no`和`cons_code`是查询关联关系的核心字段，`status`表示当前关系的状态。 |

**数据准备核心思路:**
一个有效的测试数据至少需要包含在`cm_wechat_cust_consult_relation`表中存在一条或多条记录，这些记录的`hbone_no`和`cons_code`与输入参数匹配，并且`status`为有效状态（例如 '1'）。

## 3. 输入参数 (`CustConsultRelationVO`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hboneNo` | `String` | 是 | 客户信息-一账通号 |
| `conscode` | `String` | 是 | 投顾号 |

## 4. 输出结果 (`CustConsultRelationPO`)
- **成功:** 返回 `Response.ok(List<CustConsultRelationPO>)`，其中 `List<CustConsultRelationPO>` 包含查询到的客户与投顾关系列表。
- **失败:** 返回空的列表 `[]` 或在系统异常时返回错误信息。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 单个匹配查询 | 使用单个存在的`hboneNo`和`conscode`组合进行查询 | 数据库中存在`hboneNo`='8012164854'且`conscode`='zhuhua.li'的记录。 | `hboneNo`: '8012164854', `conscode`: 'zhuhua.li' | `[{"hboneNo": "8012164854", "conscode": "zhuhua.li"}]` | 1. `Response` 列表不为 `null`。<br>2. `Response` 列表大小为1。<br>3. 返回的记录中`hboneNo`为'8012164854'，`conscode`为'zhuhua.li'。 |
| **TC-N-002** | 多个匹配查询 | 使用多个存在的`hboneNo`和`conscode`组合进行查询 | 数据库中存在`hboneNo`='8012164854',`conscode`='zhuhua.li' 和 `hboneNo`='8012164857',`conscode`='dandong.zhao'的记录。 | `hboneNo`: '8012164854', `conscode`: 'zhuhua.li'<br>`hboneNo`: '8012164857', `conscode`: 'dandong.zhao' | `[{"hboneNo": "8012164854", "conscode": "zhuhua.li"}, {"hboneNo": "8012164857", "conscode": "dandong.zhao"}]` | 1. `Response` 列表不为 `null`。<br>2. `Response` 列表大小为2。<br>3. 返回的记录分别匹配输入的参数。 |
| **TC-N-003** | 部分匹配查询 | 输入的组合中，部分能在数据库中找到匹配 | 数据库中仅存在`hboneNo`='8012164854',`conscode`='zhuhua.li'的记录。 | `hboneNo`: '8012164854', `conscode`: 'zhuhua.li'<br>`hboneNo`: 'HB999', `conscode`: 'TC999' | `[{"hboneNo": "8012164854", "conscode": "zhuhua.li"}, {"hboneNo": "HB999", "conscode": "TC999"}]` | 1. `Response` 列表不为 `null`。<br>2. `Response` 列表大小为1。<br>3. 返回的记录中`hboneNo`为'8012164854'。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `hboneNo`为`null` | 查询条件中`hboneNo`为`null` | 数据库中数据任意。 | `hboneNo`: `null`, `conscode`: 'zhuhua.li' | `[{"hboneNo": null, "conscode": "zhuhua.li"}]` | 1. `Response` 列表为空 `[]`。 |
| **TC-E-002** | `conscode`为空字符串 | 查询条件中`conscode`为空字符串 | 数据库中数据任意。 | `hboneNo`: '8012164854', `conscode`: '' | `[{"hboneNo": "8012164854", "conscode": ""}]` | 1. `Response` 列表为空 `[]`。 |
| **TC-E-003** | 输入列表为空 | 传入一个空的`voList` | 数据库中数据任意。 | `voList`: `[]` | `[]` | 1. `Response` 列表为空 `[]`。 |
| **TC-E-004** | 无匹配结果 | 输入的`hboneNo`和`conscode`组合在数据库中不存在 | 数据库中不存在`hboneNo`='HB000'的记录。 | `hboneNo`: 'HB000', `conscode`: 'TC000' | `[{"hboneNo": "HB000", "conscode": "TC000"}]` | 1. `Response` 列表为空 `[]`。 |
