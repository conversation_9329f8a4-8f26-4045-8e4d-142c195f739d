# `CmWechatGroupQueryService` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `CmWechatGroupQueryService`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.queryUserGroupInfoByHbOneNo(String)`
- **功能描述:** 根据客户的恒天one账号（hbOneNo）查询其加入的所有企微群聊列表信息。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **3个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户基本信息 | `hbone_no` | 通过`hbone_no`关联客户身份。 |
| `cm_wechat_group_user` | 存储群成员与群的关联关系 | `hbone_no`, `group_id` | 通过`hbone_no`找到用户，再通过`group_id`关联到`cm_wechat_group`表。 |
| `cm_wechat_group` | 存储企微群的基本信息 | `id` | 存储群的详细信息，如群名称、群主等。 |

**数据准备核心思路:**
一个有效的测试数据至少需要满足：
1.  在 `cm_wechat_cust_info` 表中存在一个有效的客户记录，其 `hbone_no` 为测试值。
2.  在 `cm_wechat_group` 表中存在一个或多个有效的群记录。
3.  在 `cm_wechat_group_user` 表中存在关联记录，将上述客户的 `hbone_no` 和群的 `group_id` 关联起来。

## 3. 输入参数 (`String`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 客户的恒天one账号唯一标识。 |

## 4. 输出结果 (`Response<List<UserGroupInfoVO>>`)
- **成功:** 返回 `Response.ok(Data)`，其中 `Data` 为 `List<UserGroupInfoVO>` 类型，包含用户所在的群聊信息列表。如果用户未加入任何群，返回空的列表。
- **失败:** 返回带有错误码和错误信息的 `Response` 对象。例如，业务异常时返回`{code: PARAM_ERROR.code, description: "错误描述"}`，系统异常时返回`{code: SYS_ERROR.code, description: "System error"}`。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 正常查询-用户加入单个群 | 测试输入一个已加入单个有效群聊的`hbOneNo`，系统能否正确返回该群聊信息。 | 1. `cm_wechat_cust_info` 存在 `hbOneNo`='8012164857' 的用户。<br>2. `cm_wechat_group` 存在 `id`='GROUP_1' 的群。<br>3. `cm_wechat_group_user` 存在 `hbOneNo`='8012164857' 和 `group_id`='GROUP_1' 的关联记录。 | `hbOneNo`: '8012164857' | `{"hbOneNo":"8012164857"}` | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`，是一个包含1个元素的List。<br>3. 列表中的元素包含'GROUP_1'的群信息。 |
| **TC-N-002** | 正常查询-用户加入多个群 | 测试输入一个已加入多个有效群聊的`hbOneNo`，系统能否正确返回所有群聊的信息列表。 | 1. `cm_wechat_cust_info` 存在 `hbOneNo`='9000000011' 的用户。<br>2. `cm_wechat_group` 存在 `id`='GROUP_1' 和 `id`='GROUP_2' 的群。<br>3. `cm_wechat_group_user` 存在 `hbOneNo`='9000000011' 与 'GROUP_1', 'GROUP_2' 的关联记录。 | `hbOneNo`: '9000000011' | `{"hbOneNo":"9000000011"}` | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`，是一个包含2个元素的List。<br>3. 列表中的元素包含'GROUP_1'和'GROUP_2'的群信息。 |
| **TC-N-003** | 边界场景-用户未加入任何群 | 测试输入一个有效的但未加入任何群聊的`hbOneNo`，系统应返回成功，但数据列表为空。 | 1. `cm_wechat_cust_info` 存在 `hbOneNo`='8012164854' 的用户。<br>2. `cm_wechat_group_user` 中不存在 `hbOneNo`='8012164854' 的任何记录。 | `hbOneNo`: '8012164854' | `{"hbOneNo":"8012164854"}` | 1. `Response.code` 为 `200`。<br>2. `Response.data` 是一个空的List (`[]`)。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | 异常输入-`hbOneNo`为`null` | 测试输入参数`hbOneNo`为`null`，预期系统能够处理并返回参数错误。 | 无 | `hbOneNo`: `null` | `{"hbOneNo":null}` | 1. `Response.code` 为 `PARAM_ERROR` 的错误码。<br>2. `Response.description` 包含参数错误的提示信息。 |
| **TC-E-002** | 异常输入-`hbOneNo`为空字符串 | 测试输入参数`hbOneNo`为空字符串`""`，预期系统能够处理并返回参数错误。 | 无 | `hbOneNo`: "" | `{"hbOneNo":""}` | 1. `Response.code` 为 `PARAM_ERROR` 的错误码。<br>2. `Response.description` 包含参数错误的提示信息。 |
| **TC-E-003** | 异常场景-依赖服务抛出业务异常 | 模拟`wechatGroupUserService.queryUserGroupInfoByHbOneNo`方法在执行时抛出`BusinessException`。 | `hbOneNo`='USER_D_BUSINESS_EXCEPTION'，使其在调用依赖服务时触发已知业务异常。 | `hbOneNo`: 'USER_D_BUSINESS_EXCEPTION' | `{"hbOneNo":"USER_D_BUSINESS_EXCEPTION"}` | 1. `Response.code` 为 `PARAM_ERROR` 的错误码。<br>2. `Response.description` 为 `BusinessException` 中定义的错误描述。 |
| **TC-E-004** | 异常场景-依赖服务抛出系统异常 | 模拟`wechatGroupUserService.queryUserGroupInfoByHbOneNo`方法在执行时抛出`RuntimeException`（如数据库连接失败）。 | `hbOneNo`='USER_E_SYSTEM_EXCEPTION'，使其在调用依赖服务时触发数据库超时或空指针等未知异常。 | `hbOneNo`: 'USER_E_SYSTEM_EXCEPTION' | `{"hbOneNo":"USER_E_SYSTEM_EXCEPTION"}` | 1. `Response.code` 为 `SYS_ERROR` 的错误码。<br>2. `Response.description` 为 "System error"。 |