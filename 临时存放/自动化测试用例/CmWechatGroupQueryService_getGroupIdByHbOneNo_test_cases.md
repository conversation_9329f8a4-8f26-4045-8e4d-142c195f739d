# `CmWechatGroupQueryService.getGroupIdByHbOneNo` 接口测试用例

## 1. 接口概述

- **接口名称:** `CmWechatGroupQueryService.getGroupIdByHbOneNo`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getGroupIdByHbOneNo(String)`
- **功能描述:** 根据客户的一账通号（hbOneNo）查询其当前所有正常在群的群组ID列表及对应的入群时间。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **2个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户基本信息 | `hbone_no`, `external_user_id` | 用于通过 `hbone_no` 找到客户的 `external_user_id`。 |
| `cm_wechat_group_user` | 记录客户与群组的关联 | `EXTERNAL_USER_ID`, `CHAT_ID`, `USERCHATFLAG` | 通过 `EXTERNAL_USER_ID` 筛选出客户状态为正常（`USERCHATFLAG=0`）的群组记录。 |

**数据准备核心思路:**
创建一个有效的测试数据，需要确保 `cm_wechat_cust_info` 中存在一个客户记录，该客户的 `EXTERNAL_USER_ID` 在 `cm_wechat_group_user` 中至少有一条 `USERCHATFLAG` 为 `0` 的记录。

## 3. 输入参数 (`String`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 客户的唯一一账通号。 |

## 4. 输出结果 (`GroupIdVO`)

- **成功:** 返回 `Response.ok(Data)`，其中 `Data` (`GroupIdVO`) 包含一个 `groupIdList` 列表。列表中的每个对象都包含 `groupId` 和 `joinGroupTime`。
- **失败:** 如果 `hbOneNo` 为空或无效，或者客户不存在，通常返回一个空的 `groupIdList`，而不是业务异常。

## 5. 测试用例

### 5.1. 正常场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 客户在一个群中 | 使用一个只加入了一个群的客户 `hbOneNo` 进行查询。 | 1. 客户 `8012164857` 存在于 `cm_wechat_cust_info`。<br>2. 该客户在 `cm_wechat_group_user` 中只有一条 `USERCHATFLAG=0` 的记录，关联群 `**********`。 | `hbOneNo`: `8012164857` | `{"hbOneNo": "8012164857"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 包含 `1` 个元素。<br>3. 列表中的 `groupId` 为 `**********`。 |
| **TC-N-002** | 客户在多个群中 | 使用一个加入了多个群的客户 `hbOneNo` 进行查询。 | 1. 客户 `9000000011` 存在。<br>2. 该客户在 `cm_wechat_group_user` 中有多条 `USERCHATFLAG=0` 的记录，关联多个群组。 | `hbOneNo`: `9000000011` | `{"hbOneNo": "9000000011"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 包含多个元素。<br>3. 列表中包含多个 `groupId`。 |
| **TC-N-003** | 客户有在群和已退群记录 | 查询一个既有正常在群记录，也有已退群记录的客户。 | 1. 客户 `9200000103` 存在，已有退群记录。<br>2. **待准备数据**：需要为该客户添加一条在群记录。<br>3. SQL脚本：`INSERT INTO cm_wechat_group_user (CHAT_ID, EXTERNAL_USER_ID, USERCHATFLAG, JOIN_TIME, UPDATE_TIME, COMPANY_NO) VALUES ('TEST_GROUP_001', 'oSx6K1Lt-t2C8n99_kM-cTzSYhAQ', '0', NOW(), NOW(), '1');` | `hbOneNo`: `9200000103` | `{"hbOneNo": "9200000103"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 只包含 `1` 个元素。<br>3. 列表中的 `groupId` 为 `TEST_GROUP_001`（不包含已退群的记录）。 |

### 5.2. 异常及边界场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `hbOneNo` 为 `null` | 测试当 `hbOneNo` 为 `null` 时，接口是否能正常处理并返回空列表。 | 无 | `hbOneNo`: `null` | `{"hbOneNo": null}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-002** | `hbOneNo` 为空字符串 | 测试当 `hbOneNo` 为空字符串时，接口是否能正常处理并返回空列表。 | 无 | `hbOneNo`: `""` | `{"hbOneNo": ""}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-003** | `hbOneNo` 不存在 | 使用一个在 `cm_wechat_cust_info` 中不存在的 `hbOneNo` 进行查询。 | 数据库中不存在 `hbOneNo` 为 `NON_EXISTENT_999999` 的客户。 | `hbOneNo`: `NON_EXISTENT_999999` | `{"hbOneNo": "NON_EXISTENT_999999"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-004** | 客户未加入任何群 | 查询一个存在但未加入任何群组的客户。 | 客户 `8012164854` 存在，但在 `cm_wechat_group_user` 中没有任何 `USERCHATFLAG=0` 的记录。 | `hbOneNo`: `8012164854` | `{"hbOneNo": "8012164854"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |
| **TC-E-005** | 客户已退出所有群 | 查询一个存在但已从所有群组中退出的客户。 | 客户 `9200000103` 存在，但其在 `cm_wechat_group_user` 中的所有记录 `USERCHATFLAG` 均为 `1`。 | `hbOneNo`: `9200000103` | `{"hbOneNo": "9200000103"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data.groupIdList` 为空列表。 |

## 接口代码逻辑脑图
```plantuml
@startmindmap
* getGroupIdByHbOneNo
** 输入参数校验 (hboneNo)
*** 正例: hboneNo为有效字符串
**** ...继续执行
*** 反例: hboneNo为null或空字符串
**** 调用 cmWechatCustInfoMapper.getExternalUserIDByHboneNo
***** 返回null
**** 返回空的GroupIdVO对象
** 核心业务逻辑
*** 1. 查询externalUserId
**** 调用 cmWechatCustInfoMapper.getExternalUserIDByHboneNo(hboneNo)
***** 数据库中存在hboneNo
****** 返回externalUserId
***** 数据库中不存在hboneNo
****** 返回null
*** 2. 根据externalUserId查询群信息
**** externalUserId 为空 (hboneNo不存在或无效)
***** 返回空的GroupIdVO对象
**** externalUserId 不为空
***** 调用 cmWechatGroupUserMapper.selectNormalByUserId(externalUserId)
****** 查询客户所有USERCHATFLAG=0的群记录
******* 客户在1个或多个群中 (返回List<CmWechatGroupUserNewPO>)
******** 遍历群记录
********* 创建GroupIdDetailVO
********* 设置groupId和joinGroupTime
********* 添加到list
******** 返回包含群信息的GroupIdVO
******* 客户不在任何群中 (返回空List)
******** 返回空的GroupIdVO (groupIdList为空)
** 返回结果
*** 成功: Response.ok(GroupIdVO)
**** GroupIdVO中包含客户所在的群列表
**** GroupIdVO中groupIdList为空
@endmindmap
```